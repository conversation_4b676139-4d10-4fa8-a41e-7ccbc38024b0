import { moduleIpcRequest } from "@/api/request";

// 虚拟化装置相关接口类型定义
export interface VirtualAnalogParam {
  index?: number; // 序号
  id: string;
  name: string;
  description: string;
  dataValue: number; // 幅值
  ang: number; // 相角
  isModified: boolean;
  originalDataValue: number;
  originalAng: number;
}

export interface VirtualDigitalInputParam {
  index?: number; // 序号
  id: string;
  name: string;
  description: string;
  dataValue: number; // 幅值
  originalDataValue: number;
  isModified: boolean;
}

export interface VirtualDigitalOutputParam {
  index?: number; // 序号
  id: string;
  name: string;
  description: string;
  dataValue: number; // 幅值
}

export interface VirtualFaultParam {
  originalDataValue: number;
  isModified: boolean;
  index?: number; // 序号
  id: string;
  name: string;
  description: string;
  dataValue: number; // 幅值
}

export interface VirtualLedParam {
  index?: number; // 序号
  id: string;
  name: string;
  description: string;
  value: any; // 值
}

// 创建IPC请求实例
const ipc = moduleIpcRequest("controller/debug/virtualdevice/");

// 虚拟化装置API服务
export const virtualDeviceApi = {
  /**
   * 通用查询接口 - 支持多种参数类型查询
   * cmdType: read_analoy_para（模拟量）、read_bi_para（开入量）、read_bo_para（开出量）、read_fault_para（故障量）、read_led_para（led参数）
   */
  getVirtualParamsByDevice(deviceId: string, cmdType: string, params?: any) {
    return ipc.iecInvokeWithDevice<{ list: any[]; total: number }>("getVirtualParams", { cmdType, ...params }, deviceId);
  },

  /**
   * 通用修改接口 - 支持多种参数类型修改
   * cmdType: analoy_input（模拟量）、bi_input（开入量）、fault_input（故障量）
   */
  updateVirtualParamsByDevice(deviceId: string, cmdType: string, params: any[]) {
    return ipc.iecInvokeWithDevice<any>("updateVirtualParams", { cmdType, params }, deviceId);
  },

  /**
   * 故障录波回放接口
   * 发送故障回放命令到装置
   */
  playbackWaveReplayByDevice(deviceId: string, params: { fileName: string; filePath: string; fileSize: number }) {
    return ipc.iecInvokeWithDevice<any>("playbackWaveReplay", params, deviceId);
  },

  /**
   * 获取LED参数列表 - 专门用于LED颜色参数
   */
  getLedParamList(params: { deviceId: string; [key: string]: any }) {
    console.log("🔧 Mock API getLedParamList called with:", params);

    // 直接返回LED测试数据，不使用延迟
    const mockData = {
      code: 200,
      msg: "success",
      data: {
        list: [
          { id: "1", index: 1, name: "LED_BRIGHTNESS", description: "LED亮度", value: "75" },
          { id: "2", index: 2, name: "LED_COLOR_RED", description: "LED颜色-红", value: "红色" },
          { id: "3", index: 3, name: "LED_COLOR_GREEN", description: "LED颜色-绿", value: "Green" },
          { id: "4", index: 4, name: "LED_COLOR_BLUE", description: "LED颜色-蓝", value: "B" },
          { id: "5", index: 5, name: "LED_COLOR_YELLOW", description: "LED颜色-黄", value: "黄色" },
          { id: "6", index: 6, name: "LED_COLOR_ORANGE", description: "LED颜色-橙", value: "Orange" },
          { id: "7", index: 7, name: "LED_COLOR_PURPLE", description: "LED颜色-紫", value: "紫色" },
          { id: "8", index: 8, name: "LED_COLOR_WHITE", description: "LED颜色-白", value: "White" },
          { id: "9", index: 9, name: "LED_COLOR_OFF1", description: "LED状态-关闭1", value: "关闭" },
          { id: "10", index: 10, name: "LED_COLOR_OFF2", description: "LED状态-关闭2", value: "OFF" },
          { id: "11", index: 11, name: "LED_MODE_R", description: "LED模式-R", value: "R" },
          { id: "12", index: 12, name: "LED_MODE_G", description: "LED模式-G", value: "G" },
          { id: "13", index: 13, name: "LED_STATUS_CYAN", description: "LED状态-青色", value: "青色" },
          { id: "14", index: 14, name: "LED_STATUS_MAGENTA", description: "LED状态-品红", value: "Magenta" },
          { id: "15", index: 15, name: "LED_POWER_ON", description: "LED电源开", value: "ON" }
        ],
        total: 15
      }
    };

    console.log("🔧 Mock API returning:", mockData);
    return Promise.resolve(mockData);
  }
};
