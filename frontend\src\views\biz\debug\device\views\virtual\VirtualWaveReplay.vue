<template>
  <div class="table-box">
    <ProTable
      ref="proTable"
      :columns="columns"
      :request-api="getWaveReplayList"
      :init-param="initParam"
      :pagination="true"
      :data-callback="dataCallback"
      row-key="fileName"
      table-key="virtualWaveReplay"
      :max-height="'calc(100vh - 280px)'"
    >
      <!-- 表格 header 按钮 -->
      <template #tableHeader="">
        <div class="flex flex-wrap gap-4 items-center header">
          <el-button type="primary" :icon="Upload" @click="handleAddFiles">
            {{ t("device.virtualWaveReplay.addFiles") }}
          </el-button>
        </div>
      </template>
      <!-- Expand -->
      <template #expand="scope">
        {{ scope.row }}
      </template>

      <!-- 表格操作 -->
      <template #operation="scope">
        <el-button type="primary" link :icon="VideoPlay" @click="playbackFaultRecord(scope.row)">
          {{ t("device.virtualWaveReplay.faultReplay") }}
        </el-button>
      </template>
    </ProTable>
  </div>

  <!-- 文件选择对话框 -->
  <CustomFileSelector ref="fileSelectorRef" :multiple="true" :file-types="['.cfg', '.dat', '.hdr', '.inf']" @confirm="handleFilesSelected" />

  <ProgressDialog ref="progressDialog"></ProgressDialog>
</template>

<script setup lang="tsx" name="VirtualWaveReplay">
import { ref, reactive, watch, nextTick, onMounted, onBeforeUnmount } from "vue";
import { useI18n } from "vue-i18n";
import { FileItem, UrpcFileItem } from "@/api/interface/biz/debug/fileitem";
import ProTable from "@/components/ProTable/index.vue";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import { Upload, VideoPlay } from "@element-plus/icons-vue";
import { devicefileApi } from "@/api/modules/biz/debug/devicefile";
import { virtualDeviceApi } from "@/api/modules/biz/debug/virtualDevice";
import { MathUtils } from "@/utils/mathUtils";
import { ElMessageBox, ElMessage } from "element-plus";
import { createDebouncedSave, safeLocalStorageGet, EventListenerManager } from "@/utils/performance";
import ProgressDialog from "../../dialog/ProgressDialog.vue";
import CustomFileSelector from "@/components/CustomFileSelector/index.vue";
import { useDebugStore } from "@/stores/modules/debug";

const { addConsole } = useDebugStore();

// 透传装置ID
const props = defineProps<{ deviceId: string }>();
const progressDialog = ref();
const fileSelectorRef = ref();

// 事件监听器管理
const eventManager = new EventListenerManager();

// ProTable 实例
const proTable = ref<ProTableInstance>();
// 如果表格需要初始化请求参数，直接定义传给 ProTable (之后每次请求都会自动带上该参数，此参数更改之后也会一直带上，改变此参数会自动刷新表格数据)
const initParam = reactive({ filePath: "/shr/wave_replay" });

// 默认使用 /shr/wave_replay 目录
const selectType = ref("/shr/wave_replay");

const { t } = useI18n();

// 添加录波回放文件
const handleAddFiles = () => {
  fileSelectorRef.value?.open();
};

// 处理文件选择完成
const handleFilesSelected = async (selectedData: any) => {
  // 处理单个文件或文件数组
  let selectedFiles = [];
  if (Array.isArray(selectedData)) {
    selectedFiles = selectedData;
  } else if (selectedData) {
    selectedFiles = [selectedData];
  }

  if (!selectedFiles || selectedFiles.length === 0) {
    return;
  }

  try {
    progressDialog.value.show();

    // 构建文件下载参数 - 从FileItem对象中提取path属性
    const fileItems = [];

    for (let i = 0; i < selectedFiles.length; i++) {
      const fileItem = selectedFiles[i];
      // 提取文件路径，确保数据完全可序列化
      const filePath = fileItem.path || fileItem;
      fileItems.push({
        filePath: String(filePath),
        fileRow: i + 1
      });
    }

    // 创建纯净的参数对象
    const downloadParams = {
      remoteParentPath: String(selectType.value),
      fileItems: fileItems
    };

    console.log("下载参数:", JSON.stringify(downloadParams, null, 2));

    // 调用下载文件接口，将文件下载到装置的 /shr/wave_replay 目录
    const result = await devicefileApi.downloadDeviceFileByDevice(String(props.deviceId), downloadParams);

    if (Number(result.code) === 0) {
      ElMessage.success(t("device.virtualWaveReplay.messages.downloadSuccess"));
      addConsole(t("device.virtualWaveReplay.messages.downloadSuccess"));
      // 刷新表格数据
      proTable.value?.refresh();
    } else {
      ElMessage.error(t("device.virtualWaveReplay.messages.downloadFailed") + ": " + result.msg);
    }
  } catch (error) {
    console.error("文件下载失败:", error);
    ElMessage.error(t("device.virtualWaveReplay.messages.downloadFailed"));
  } finally {
    progressDialog.value.hide();
  }
};

// 故障录波回放功能
const playbackFaultRecord = async (row: FileItem) => {
  console.log("playbackFaultRecord", row);

  if (String(row.fileName).endsWith("\\")) {
    ElMessageBox.alert(t("device.virtualWaveReplay.errors.invalidFile"), t("common.error"), {
      type: "error",
      confirmButtonText: t("device.virtualParam.confirm")
    });
    return;
  }

  if (row.fileSize === 0) {
    ElMessageBox.alert(t("device.virtualWaveReplay.errors.fileSizeZero", { fileName: row.fileName }), t("common.error"), {
      type: "error",
      confirmButtonText: t("device.virtualParam.confirm")
    });
    return;
  }

  try {
    progressDialog.value.show();

    // 调用后台接口实现故障回放
    // 这里调用虚拟装置的故障回放接口
    const result = await virtualDeviceApi.playbackWaveReplayByDevice(props.deviceId, {
      fileName: row.fileName,
      filePath: selectType.value + "/" + row.fileName,
      fileSize: row.fileSize
    });

    if (Number(result.code) === 0) {
      ElMessage.success(t("device.virtualWaveReplay.messages.playbackStarted", { fileName: row.fileName }));
      addConsole(t("device.virtualWaveReplay.messages.playbackStarted", { fileName: row.fileName }));
    } else {
      ElMessage.error(t("device.virtualWaveReplay.errors.playbackFailed") + ": " + result.msg);
    }
  } catch (error) {
    console.error("故障录波回放失败:", error);
    ElMessageBox.alert(t("device.virtualWaveReplay.errors.playbackFailed"), t("common.error"), {
      type: "error",
      confirmButtonText: t("device.virtualParam.confirm")
    });
  } finally {
    progressDialog.value.hide();
  }
};

// dataCallback 是对于返回的表格数据做处理，如果你后台返回的数据不是 list && total 这些字段，可以在这里进行处理成这些字段
// 或者直接去 hooks/useTable.ts 文件中把字段改为你后端对应的就行
const dataCallback = (data: any) => {
  return {
    list: data.list,
    total: data.total
  };
};

// 获取文件列表
const getWaveReplayList = async (params: any) => {
  let newParams = JSON.parse(JSON.stringify(params));
  newParams.createTime && (newParams.startTime = newParams.createTime[0]);
  newParams.createTime && (newParams.endTime = newParams.createTime[1]);
  newParams.filePath = selectType.value;
  delete newParams.createTime;

  try {
    progressDialog.value.show();
    const response = await devicefileApi.getDeviceFileByDevice(props.deviceId, newParams);
    if (Number(response.code) === 0) {
      const items = Array.isArray(response.data) ? response.data : [];
      items.forEach(item => {
        item.fileSizeAs = MathUtils.formatBytes(item.fileSize);
      });
      return {
        list: items,
        total: items.length
      };
    } else {
      ElMessage.error(t("device.virtualWaveReplay.errors.getFilesFailed") + ": " + response.msg);
      return {
        list: [],
        total: 0
      };
    }
  } catch (error) {
    console.error("获取文件列表失败:", error);
    ElMessage.error(t("device.virtualWaveReplay.errors.getFilesFailed"));
    return {
      list: [],
      total: 0
    };
  } finally {
    progressDialog.value.hide();
  }
};

// 表格配置项
const columns = reactive<ColumnProps<FileItem>[]>([
  { type: "index", label: t("device.virtualWaveReplay.serialNumber"), fixed: "left", width: 70 },
  {
    prop: "fileName",
    label: t("device.virtualWaveReplay.fileName"),
    width: 300,
    sortable: true,
    search: { el: "input", placeholder: t("device.virtualWaveReplay.searchFileName") }
  },
  {
    prop: "fileSize",
    label: t("device.virtualWaveReplay.fileSize"),
    width: 120,
    sortable: true,
    sortMethod: (a: any, b: any) => {
      // a、b 是整行数据，这里按原始字节数比较
      const sizeA = Number(a?.fileSize) || 0;
      const sizeB = Number(b?.fileSize) || 0;
      return sizeA - sizeB;
    },
    render: scope => {
      // 显示格式化后的文件大小
      return <span>{scope.row.fileSizeAs}</span>;
    }
  },
  {
    prop: "lastModified",
    label: t("device.virtualWaveReplay.lastModified"),
    sortable: true,
    sortMethod: (a: any, b: any) => {
      // 按时间排序
      const timeA = new Date(a.lastModified).getTime() || 0;
      const timeB = new Date(b.lastModified).getTime() || 0;
      return timeA - timeB;
    }
  },
  { prop: "operation", label: t("device.virtualWaveReplay.operation"), fixed: "right", width: 120 }
]);

// 删除了不需要的函数，现在使用ProTable的内置功能

// 使用性能优化的保存函数
const debouncedSaveData = createDebouncedSave(props.deviceId + ".waveReplayData", 1000);

onMounted(() => {
  addAllListeners();
});

onBeforeUnmount(() => {
  // 立即保存一次数据
  debouncedSaveData.flush();
  removeAllListeners();
});

function removeAllListeners() {
  eventManager.cleanup();
}

function addAllListeners() {
  const saveHandler = () => debouncedSaveData.flush();
  window.addEventListener("beforeunload", saveHandler);
  eventManager.add("beforeunload", saveHandler);
}
</script>

<style lang="css" scoped>
.table-box {
  display: flex;
  flex: 1;
  flex-direction: column;
  width: 100%;
}
.header {
  margin-bottom: 8px;
}
</style>
